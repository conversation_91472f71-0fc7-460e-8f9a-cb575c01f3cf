# TECU1000 雷视轨迹接力算法 (分体式)

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()
[![Version](https://img.shields.io/badge/version-1.5.250603.0-blue.svg)]()
[![Config Version](https://img.shields.io/badge/config-20250603.1-orange.svg)]()

## 项目简介

TECU1000雷视轨迹接力算法是一套专为边缘智能盒设计的目标轨迹融合系统。该算法主要应用于隧道、高速公路等场景，通过前后追打、对射等方式安装的雷视一体机设备，实现多设备间的目标轨迹拼接和融合，生成连续的目标轨迹并重新分配拼接ID。

### 核心功能

- **多设备轨迹融合**: 支持多个雷视一体机设备的目标轨迹拼接
- **智能目标关联**: 基于卡尔曼滤波的目标预测和关联算法
- **多盒子接力**: 支持多个边缘智能盒之间的轨迹接力传递
- **实时标定**: 支持设备间的自动标定和全息映射
- **车道级修正**: 基于车道信息的目标轨迹修正
- **碰撞检测**: 车辆碰撞检测与轨迹纠正功能

### 技术特点

- **高性能**: 支持实时处理，适用于高速场景
- **可配置**: 丰富的配置参数，支持不同场景适配
- **模块化**: 清晰的模块划分，便于维护和扩展
- **跨平台**: 支持x86和ARM64架构

## 项目结构

```
├── README.md                           # 项目说明文档
├── CMakeLists.txt                      # 主CMake配置文件
├── CMakeLists_x86.txt                  # x86平台CMake配置
├── CMakeLists_310B.txt                 # ARM64平台CMake配置
├── CMakeLists_multi_box.txt            # 多盒子测试CMake配置
├── run_x86.sh                          # x86平台编译脚本
├── run_310B.sh                         # ARM64平台编译脚本
├── backup_branch.sh                    # 分支备份脚本
├── Src/                                # 核心算法源码
│   ├── algorithm_lib.cpp               # 算法库主接口
│   ├── pipeline_track_fusion.cpp      # 轨迹融合管道
│   ├── tecu1000_algorithm_header.h     # 算法头文件
│   └── ...
├── TracksSplicing/                     # 轨迹拼接模块
│   ├── tracks_splicing.cpp             # 轨迹拼接核心算法
│   ├── radar_vision_fusion.cpp        # 雷视融合算法
│   └── ...
├── Config/                             # 配置管理模块
├── KalmanFilter/                       # 卡尔曼滤波模块
├── Calib/                              # 标定模块
├── LaneSmoothing/                      # 车道平滑模块
├── CollisionDetection/                 # 碰撞检测模块
├── ConfigFile/                         # 配置文件
│   ├── config_algorithm.txt            # 主配置文件
│   ├── config_algorithm_box1.txt       # 第一个盒子配置
│   └── config_algorithm_box2.txt       # 第二个盒子配置
├── algorithm/                          # 算法运行目录
│   ├── data/                           # 测试数据
│   └── log/                            # 日志输出
├── tecu1000_multi_box_relay_test.cpp   # 多盒子接力测试程序
├── 多盒子接力测试说明.md                # 多盒子测试详细说明
└── 多盒子接力测试解决方案说明.md        # 多盒子测试解决方案
```

## 快速开始

### 环境要求

- **操作系统**: Linux (Ubuntu 18.04+)
- **编译器**: GCC 7.0+ 或 Clang 6.0+
- **CMake**: 3.10+
- **依赖库**:
  - OpenCV 4.0+ (可选，用于视觉功能)
  - jsoncpp
  - pthread

### 编译安装

#### 1. x86平台编译 (推荐用于开发调试)

```bash
# 使用编译脚本 (推荐)
./run_x86.sh

# 或手动编译
cp CMakeLists_x86.txt CMakeLists.txt
mkdir -p build_x86
cd build_x86
cmake ..
make -j$(nproc)
```

#### 2. ARM64平台编译 (用于部署)

```bash
# 使用编译脚本
./run_310B.sh

# 或手动编译
cp CMakeLists_310B.txt CMakeLists.txt
mkdir -p build
cd build
cmake ..
make -j$(nproc)
```

#### 3. 多盒子测试编译

```bash
# 编译多盒子接力测试程序
cp CMakeLists_multi_box.txt CMakeLists.txt
mkdir -p build
cd build
cmake ..
make tecu1000_multi_box_relay_test_v2 -j4
```

### 配置说明

主要配置文件位于 `ConfigFile/config_algorithm.txt`，包含以下关键参数：

```bash
# 数据保存配置
save_data 0                             # 是否保存数据 0:不保存 1:保存
save_simulation 0                       # 是否保存仿真数据
save_match_error 1                      # 是否保存重叠区域匹配误差

# 设备标定配置
calib_min_cnt 3                         # 设备标定最小保存数量
calib_compute_cnt 2                     # 标定矩阵计算点数量
calib_xerror_threshold 1.5              # x方向误差阈值 (m)
calib_yerror_threshold 2                # y方向误差阈值 (m)

# 轨迹拼接配置
using_kalman_filter 1                   # 是否使用卡尔曼滤波 0:关闭 1:开启
max_detect_distance 500.1               # 最远探测距离 (m)
min_detect_distance 20.1                # 最近探测距离 (m)
match_xcoor_distance_theshold 3.85      # x方向关联误差阈值
match_ycoor_distance_theshold 40.0      # y方向关联误差阈值

# 版本信息
config_version 20241122.1.0             # 配置版本号
```

## 使用方法

### 基本使用

```bash
# 运行主程序
./tecu1000_debug <data_dir_path> <input_data_timems>

# 示例
./tecu1000_debug algorithm/data/channel_id_0 2024_05_23_15_32_49
```

### 多盒子接力测试

```bash
# 运行多盒子接力测试
./tecu1000_multi_box_relay_test_v2 <data_dir_path> <input_data_timems>

# 示例
./tecu1000_multi_box_relay_test_v2 algorithm/data/channel_id_0 2024_05_23_15_32_49
```

详细的多盒子测试说明请参考：[多盒子接力测试说明.md](多盒子接力测试说明.md)

## 开发计划
1. 中间新目标起批逻辑  解决目标接力失败后消失的问题 (从未起批的目标中查找符合条件的目标进行二次起批)
2. 阈值自适应  减少部分超参数的设置
3. 盲区预测新方法  采用深度学习的方法

## 开发记录

<details>
<summary>1.5.250603.0 (最新版本)</summary>

- 配置文件中 log_info_level增加 9: 不执行算法
- 算法版本号：1.5.250603.0
- 配置参数版本：20250603.1
</details>

<details>
<summary>1.5.20250527.1</summary>

- 增加多盒子接力测试程序v2，支持分时序列处理
- 算法版本号：1.5.20250527.1
- 配置参数版本：20241122.1.0
</details>

<details>
<summary>1.5.20250516.1</summary>

- 增加run_310B.sh和run_x86.sh，支持本地debug和arm64两种编译方式
- 集成测试脚本，简化编译流程
- 增加backup_branch.sh备份脚本
- 算法版本号：1.5.20250516.1
- 配置参数版本：20241122.1.0
</details>

<details>
<summary>1.4.250522.0</summary>

- 修改目标ID检查策略，优化ID管理
```cpp
if (target.id > 1000) {
   // 删除第一位数字，保留后三位
   target.id = target.id % 1000;
}
```
</details>

<details>
<summary>1.4.250521.2</summary>

- 通过配置文件设置日志等级
- 支持三级日志控制：0-不打印日志，1-打印简单日志，2-打印完整日志
- 配置参数：`log_info_level 2`
</details>

<details>
<summary>1.4.250521.1</summary>

- 修复车道方向计算bug，支持根据车道方向正确计算车辆方向
- 增加输入数据检验功能，对错误的目标ID进行修正
- 目标ID修正规则：设备ID * 1000 + 目标ID
- 增加输入帧率限制功能
</details>

<details>
<summary>20250425</summary>

- 增加算法适配器层，统一算法接口
- 添加回调函数接口支持
- 注意：回调函数接口尚未完成测试
</details>

## API 接口说明

### 核心接口

#### 1. 算法初始化

```cpp
/**
 * @Name: algorithm_init
 * @Description: 初始化算法实例，加载配置文件并设置通道参数
 *
 * @Input
 * - channel_id: 通道ID，用于区分不同的算法实例
 * - config_path: 配置文件路径，包含算法运行参数
 * - edgebox_id: 边缘盒ID，用于多盒子场景的设备标识
 *
 * @Output
 * - 返回值: 0-成功，-1-失败
 *
 * @Edit History
 * Date: 2025-01-XX
 * Time: XX:XX:XX
 * Author: JiaTao
 * Content: 算法初始化接口
 */
int algorithm_init(int channel_id, const char* config_path, int edgebox_id);
```

#### 2. 目标输入处理

```cpp
/**
 * @Name: target_input
 * @Description: 接收多设备目标输入数据并进行轨迹融合处理
 *
 * @Input
 * - channel_id: 通道ID
 * - multi_device_targets: 多设备目标数据结构
 * - timestamp_ms: 时间戳（毫秒）
 *
 * @Output
 * - 返回值: 0-成功，-1-失败
 *
 * @Edit History
 * Date: 2025-01-XX
 * Time: XX:XX:XX
 * Author: JiaTao
 * Content: 目标输入处理接口
 */
int target_input(int channel_id, MultiDeviceTargets multi_device_targets, long long timestamp_ms);
```

#### 3. 多盒子目标输入

```cpp
/**
 * @Name: multi_box_target_input
 * @Description: 接收多盒子目标输入数据，支持盒子间的轨迹接力
 *
 * @Input
 * - channel_id: 通道ID
 * - multi_box_targets: 多盒子目标数据结构
 * - timestamp_ms: 时间戳（毫秒）
 *
 * @Output
 * - 返回值: 0-成功，-1-失败
 *
 * @Edit History
 * Date: 2025-01-XX
 * Time: XX:XX:XX
 * Author: JiaTao
 * Content: 多盒子目标输入接口
 */
int multi_box_target_input(int channel_id, MultiBoxTargets multi_box_targets, long long timestamp_ms);
```

### 数据结构

#### 目标数据结构

```cpp
// 单个目标信息
struct Target {
    int id;                    // 目标ID
    float x, y, z;            // 目标位置坐标 (m)
    float vx, vy, vz;         // 目标速度 (m/s)
    float length, width, height; // 目标尺寸 (m)
    int vehicle_type;         // 车辆类型
    float vt_score;           // 车辆类型置信度
    int target_type;          // 目标类型
    float tt_score;           // 目标类型置信度
};

// 设备输入目标
struct InputTargets {
    int device_id;            // 设备ID
    std::vector<Target> targets; // 目标列表
};

// 多设备目标
struct MultiDeviceTargets {
    std::vector<InputTargets> device_input; // 多设备输入
};
```

## 测试指南

### 单元测试

项目包含完整的测试套件，支持以下测试场景：

1. **基础功能测试**
   ```bash
   # 编译测试程序
   ./run_x86.sh

   # 运行基础测试
   ./tecu1000_debug algorithm/data/channel_id_0 2024_05_23_15_32_49
   ```

2. **多盒子接力测试**
   ```bash
   # 编译多盒子测试
   cp CMakeLists_multi_box.txt CMakeLists.txt
   cd build && cmake .. && make tecu1000_multi_box_relay_test_v2 -j4

   # 运行多盒子测试
   ./tecu1000_multi_box_relay_test_v2 algorithm/data/channel_id_0 2024_05_23_15_32_49
   ```

### 性能测试

```bash
# 性能基准测试
time ./tecu1000_debug algorithm/data/channel_id_0 2024_05_23_15_32_49

# 内存使用监控
valgrind --tool=memcheck --leak-check=full ./tecu1000_debug algorithm/data/channel_id_0 2024_05_23_15_32_49
```

### 测试数据格式

测试数据目录结构：
```
algorithm/data/channel_id_0/
├── 2024_05_23_15_32_49.dat    # 目标数据文件
├── 2024_05_23_15_32_49.txt    # 时间戳文件
└── devicelist.dat             # 设备列表文件
```

## 故障排除

### 常见问题

#### 1. 编译错误

**问题**: 找不到头文件或库文件
```bash
fatal error: opencv2/opencv.hpp: No such file or directory
```

**解决方案**:
```bash
# Ubuntu/Debian
sudo apt-get install libopencv-dev

# CentOS/RHEL
sudo yum install opencv-devel

# 或者禁用OpenCV功能
# 编辑CMakeLists.txt，注释掉OpenCV相关部分
```

#### 2. 运行时错误

**问题**: 配置文件加载失败
```
ERROR: 无法加载配置文件: ConfigFile/config_algorithm.txt
```

**解决方案**:
```bash
# 检查配置文件是否存在
ls -la ConfigFile/config_algorithm.txt

# 检查文件权限
chmod 644 ConfigFile/config_algorithm.txt

# 检查配置文件格式
head -10 ConfigFile/config_algorithm.txt
```

#### 3. 数据处理错误

**问题**: 目标数据格式错误
```
ERROR: 目标数据解析失败
```

**解决方案**:
```bash
# 检查数据文件格式
file algorithm/data/channel_id_0/2024_05_23_15_32_49.dat

# 检查设备列表文件
cat algorithm/data/channel_id_0/devicelist.dat

# 验证时间戳文件
head -5 algorithm/data/channel_id_0/2024_05_23_15_32_49.txt
```

### 调试技巧

#### 1. 启用详细日志

```bash
# 修改配置文件中的日志级别
echo "log_info_level 2" >> ConfigFile/config_algorithm.txt
```

#### 2. 使用GDB调试

```bash
# 编译调试版本
g++ -g -O0 -DDEBUG ...

# 使用GDB调试
gdb ./tecu1000_debug
(gdb) run algorithm/data/channel_id_0 2024_05_23_15_32_49
(gdb) bt  # 查看调用栈
```

#### 3. 内存泄漏检测

```bash
# 使用Valgrind检测内存问题
valgrind --tool=memcheck --leak-check=full --show-leak-kinds=all ./tecu1000_debug algorithm/data/channel_id_0 2024_05_23_15_32_49
```

## 性能优化

### 编译优化

```bash
# 启用编译器优化
g++ -O3 -march=native -DNDEBUG ...

# 启用链接时优化
g++ -flto ...
```

### 运行时优化

1. **内存池管理**: 使用内存池减少动态内存分配
2. **SIMD优化**: 利用向量指令加速计算密集型操作
3. **多线程处理**: 合理使用多线程提高并发性能
4. **缓存优化**: 优化数据结构布局，提高缓存命中率

### 性能监控

```bash
# CPU使用率监控
top -p $(pgrep tecu1000_debug)

# 内存使用监控
ps aux | grep tecu1000_debug

# 系统调用跟踪
strace -c ./tecu1000_debug algorithm/data/channel_id_0 2024_05_23_15_32_49
```

## 贡献指南

### 开发环境设置

1. **克隆仓库**
   ```bash
   git clone <repository_url>
   cd tecu1000_0_algorithm
   ```

2. **安装依赖**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install build-essential cmake libopencv-dev libjsoncpp-dev

   # CentOS/RHEL
   sudo yum install gcc-c++ cmake opencv-devel jsoncpp-devel
   ```

3. **配置开发环境**
   ```bash
   # 设置编译选项
   export CXX=g++
   export CXXFLAGS="-std=c++11 -Wall -Wextra"
   ```

### 代码规范

#### 1. 命名规范

- **文件名**: 使用小写字母和下划线，如 `tracks_splicing.cpp`
- **类名**: 使用驼峰命名法，如 `TrackSplicing`
- **函数名**: 使用小写字母和下划线，如 `target_input`
- **变量名**: 使用小写字母和下划线，如 `device_id`
- **常量名**: 使用大写字母和下划线，如 `MAX_DEVICE_COUNT`

#### 2. 注释规范

```cpp
/*
 * @Name: function_name
 * @Description: 函数功能描述
 *
 * @Input
 * - param1: 参数1描述
 * - param2: 参数2描述
 *
 * @Output
 * - return: 返回值描述
 *
 * @Edit History
 * Date: 2025-01-XX
 * Time: XX:XX:XX
 * Author: JiaTao
 * Content: 修改内容描述
 */
```

#### 3. 代码格式

- 使用4个空格缩进，不使用Tab
- 行长度不超过120字符
- 大括号采用K&R风格
- 在运算符前后添加空格

### 提交规范

#### 1. 提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(tracking): 添加多盒子轨迹接力功能

- 实现多盒子间的目标轨迹传递
- 支持配置不同盒子的设备分配
- 添加接力模式的配置参数

Closes #123
```

#### 2. 分支管理

- `main`: 主分支，保持稳定
- `develop`: 开发分支，集成新功能
- `feature/*`: 功能分支，开发新功能
- `hotfix/*`: 热修复分支，修复紧急问题

### 测试要求

1. **单元测试**: 新增功能必须包含对应的单元测试
2. **集成测试**: 确保与现有功能的兼容性
3. **性能测试**: 关键路径需要进行性能测试
4. **文档更新**: 更新相关的API文档和使用说明

## 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 联系方式

- **项目维护者**: JiaTao
- **技术支持**: [技术支持邮箱]
- **问题反馈**: [GitHub Issues](项目Issues链接)

## 致谢

感谢所有为本项目做出贡献的开发者和测试人员。

---

**注意**: 本文档会随着项目的发展持续更新，请定期查看最新版本。