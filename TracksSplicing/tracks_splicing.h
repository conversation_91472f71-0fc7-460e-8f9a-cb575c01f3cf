#pragma once
#include "tracks_splicing_header.h"
#include "calib_header.h"
#include "kuhn_munkres.h"
#include "radar_vision_fusion.h"
#include "kalman_filter.h"
#include "config.h"
#include "calib_radar_camera.h"
#include "RoadDirectionCacl.h"
#include "vehicleLaneSmoothing.h"
#include "TargetSmoother.h"
#include "collisionDetection.h"
#include <unordered_set>

// #define DEBUG_MAKE_SMOOTH_DATA

namespace tecu_r1000_0_algorithm
{
    class TrackSplicing
    {
    public:
        /**
         * 构造函数
         * @param channel_id 通道ID
         * @param device_list 设备列表（设备按顺序排列，第0位为前方设备，第cnt-1位为后方设备）
         * @param devices_map 设备映射关系
         * @param track_splicing 拼接相关配置参数
         */
        TrackSplicing(int channel_id, DeviceList device_list, DevicesMap devices_map, Config track_splicing);
        ~TrackSplicing();

        /**
         * 功能：更新拼接配置参数（在目标拼接时调用）
         * @param device_list 当前设备列表
         * @param devices_map 设备映射信息
         * @param rccalib_vec 各设备的标定信息
         * @param edgebox_id 边缘框ID
         */
        void tracks_splicing_config_update(DeviceList device_list,
                                           DevicesMap devices_map,
                                           CalibCameraRadar *rccalib_vec,
                                           int edgebox_id);

        /**
         * 功能：目标拼接主流程算法
         * @param multi_device_targets 多设备检测到的目标集合
         * @param timestamp_ms 当前时间戳（毫秒）
         */
        void tracks_splicing_main(MultiDeviceTargets multi_device_targets, long long timestamp_ms);

        /**
         * 功能：目标拼接主流程算法 支持多盒子接力
         * @param multi_box_targets 多设备检测到的目标集合
         * @param timestamp_ms 当前时间戳（毫秒）
         */
        void tracks_splicing_main(tecu1000_alg::MultiBoxTargets& multi_box_targets, long long timestamp_ms);


        // 输出拼接后的目标，用于显示
        OutputTargets splicing_output_targets;

        // 输出拼接后的目标，用于传递给下一个盒子
        OutputTargets splicing_output_targets_next;

        // 设备列表：设备按照从前到后的顺序排列，默认第0位为前方设备，第cnt-1位为后方设备
        DeviceList splicing_devices_list;

        // 车道平滑处理对象
        VehicleLaneSmoothing *laneSmooth;
        // 车辆碰撞检测对象
        CollisionDetection *collisionDetection;
        // 拼接算法实时时间戳（毫秒）
        long long splicing_timestamp_ms;

        // 是否开启平台接力模式
        unsigned int is_platform_relay_mode;

    private:
        std::unordered_map<int, TargetSmoother> _smoothers;  // 每个目标的平滑器
        void smooth_target_output(OutputTargets& targets);

        /**
         * 功能：目标平滑处理
         * @param targets 目标集合
         */
        void smooth_target_output(std::vector<SplicingTarget>& targets);

        
#pragma region 设备校准及融合相关变量
        // 设备标定信息，各设备的标定参数
        CalibCameraRadar *splicing_rvcalib;
        // 雷达与视觉融合模块
        RadarVisionFusion *BcrFusion;
#pragma endregion

#pragma region 输入目标数据缓存
        // 输入目标路径时间阈值（用于判断目标路径是否有效）
        long long splicing_input_target_path_time_threshold;
        // 输入目标保存时间阈值（用于保存丢失的目标数据）
        long long splicing_input_target_save_time_threshold;
        // 设备稳定检测距离阈值
        float splicing_device_stably_detect_distance_threshold;
        // 设备稳定跟踪距离阈值
        float splicing_device_stably_track_distance_threshold;
        // 输入目标数据缓冲区
        std::vector<SplicingInputTarget> splicing_input_target_vec;

        // 平滑的仿真数据生成
        #ifdef DEBUG_MAKE_SMOOTH_DATA
            // 仿真数据生成
            OutputTargets splicing_output_targets_smooth;
            void make_smooth_data();
        #endif

        /**
         * 功能：更新输入目标数据缓冲区
         * @param multi_device_targets 多设备检测到的目标集合
         */
        void splicing_input_target_vec_update(const MultiDeviceTargets& multi_device_targets);

        /**
         * 功能：更新输入目标数据缓冲区
         * @param last_output_targets 来自上一个盒子的最后一个设备接管的设备输出 带有拼接信息
         * @param device_info_next 下一个盒子的设备信息
         */
        void splicing_input_target_vec_update(const OutputTargets& last_output_targets);

        /**
         * 功能：对输入目标数据进行去噪处理
         * @param splicing_input_target_vec_ 输入目标数据缓冲区（引用）
         */
        void splicing_input_target_vec_denoise();

        /**
         * 功能：对输入目标数据进行插值处理
         * @param splicing_input_target_vec_ 输入目标数据缓冲区（引用）
         * @param splicing_time_ms 当前拼接时间戳（毫秒）
         */
        void splicing_input_target_interploation(std::vector<SplicingInputTarget> &splicing_input_target_vec_, long long splicing_time_ms);

        /**
         * 功能：清理输入目标数据缓冲区
         */
        void splicing_input_target_vec_clean();

        /**
         * 功能：将设备坐标下的点映射到基坐标系
         * @param coor_in_device 设备坐标系中的点
         * @param coor_in_base 输出的基坐标系中的点
         * @param device_id 设备ID
         * @return 映射成功返回0，失败返回-1
         */
        int splicing_input_target_map(CalibPoint coor_in_device, CalibPoint &coor_in_base, unsigned int device_id);

        /**
         * 功能：设置目标所在区域状态
         * @param target_area_status 输出的目标区域状态
         * @param coor_in_device 设备坐标系中的点
         * @param device_id 设备ID
         * @param splicing_device_list 设备列表
         * @param yspeed 目标Y方向速度
         */
        void splicing_target_area_status(SplicingArea &target_area_status,
                                         CalibPoint coor_in_device,
                                         unsigned int device_id,
                                         DeviceList splicing_device_list,
                                         float yspeed);
#pragma endregion

#pragma region 目标运动方向计算
        // 上次目标方向更新的时间（毫秒）
        long long target_direction_update_time_ms;

        /**
         * 功能：根据输入目标数据计算目标运动方向
         * @param splicing_input_target_vec 输入目标数据缓冲区
         * @param splicing_devices_list 设备列表
         * @param splicing_timestamp_ms 当前时间戳（毫秒）
         */
        void get_target_direction(std::vector<SplicingInputTarget> &splicing_input_target_vec,
                                  DeviceList splicing_devices_list,
                                  long long splicing_timestamp_ms);

        // 道路方向计算模块
        RoadDirectionCacl *roadDirectionCacl;
#pragma endregion

#pragma region 设备目标匹配算法
        // 使用匈牙利算法进行目标匹配
        KuhnMunkres *splicing_km = nullptr;
		
        // 匹配结果的ID集合
        std::vector<SpilcingMatchIds> spilcing_match_ids_vec;
        // 匹配时的速度阈值
        float splicing_match_speed_threshold;
        // 匹配时的时间阈值
        long long splicing_match_time_threshold;
        // 默认匹配权重
        double  spilcing_default_match_weight;
        // 匹配得分阈值
        float splicing_match_score_threshold;
        // 匹配时速度比例系数
        float splicing_speed_rate;
        // 批量匹配最小时间阈值
        long long splicing_batch_min_time;
        // 批量匹配最大时间阈值
        long long splicing_batch_max_time;
        // 批量匹配数量比例阈值
        float splicing_batch_count_rate_threshold;
        // 批量匹配得分阈值
        float splicing_batch_score_threshold;
        // 车牌匹配时第一候选得分阈值
        float splicing_plate_match_top1_score;
        // 车牌匹配时第二候选得分阈值
        float splicing_plate_match_top2_score;
        // 目标速度持续时间阈值
        long long splicing_speed_time_threshold;
        // 匹配成功时X坐标距离阈值
        float splicing_match_xcoor_distance_theshold;
        // 匹配成功时Y坐标距离阈值
        float splicing_match_ycoor_distance_theshold;
        // 批量匹配时X坐标距离阈值
        float splicing_batch_xcoor_distance_theshold;
        // 批量匹配时Y坐标距离阈值
        float splicing_batch_ycoor_distance_theshold;
        // 批量匹配中，目标长时间未匹配认为匹配失败的时间阈值
        long long splicing_batch_lose_time_threshold;
        // 目标方向匹配阈值
        float splicing_match_direction_threshold;

        /**
         * 功能：批量更新匹配目标的ID分配
         * @param splicing_overlap_target_vec 重叠目标集合
         * @param splicing_match_weights 匹配权重矩阵
         * @param splicing_match 输出匹配结果数组
         */
        void spilcing_batch_id_update(std::vector<SplicingInputTarget> splicing_overlap_target_vec, std::vector<std::vector<double>> splicing_match_weights, int *splicing_match);

        /**
         * 功能：更新匹配成功的目标ID分配
         * @param splicing_overlap_target_vec 重叠目标集合
         */
        void spilcing_match_id_update(std::vector<SplicingInputTarget> splicing_overlap_target_vec);

        /**
         * 功能：根据匹配权重矩阵进行匹配，返回匹配结果数组
         * @param splicing_overlap_target_vec 重叠目标集合
         * @param splicing_match_weights 匹配权重矩阵（引用）
         * @return 指向匹配结果数组的指针
         */
        int* spilcing_match_ids_vec_match(std::vector<SplicingInputTarget> splicing_overlap_target_vec, std::vector<std::vector<double>> &splicing_match_weights);

        /**
         * 功能：批量更新匹配ID集合
         */
        void spilcing_match_id_vec_batch();

        /**
         * 功能：校正匹配错误的目标ID对应关系
         * @param splicing_overlap_target_vec 重叠目标集合
         */
        void splicing_mismatch_correct(std::vector<SplicingInputTarget> splicing_overlap_target_vec);

        /**
         * 功能：更新当前匹配ID集合
         */
        void spilcing_match_ids_vec_update();

        /**
         * 功能：清空匹配ID集合
         */
        void spilcing_match_ids_vec_clean();

        /**
         * 功能：计算拼接目标的坐标（基于两个设备检测结果）
         * @param device1_target 第一个设备检测到的目标
         * @param device2_target 第二个设备检测到的目标
         * @return 计算得到的基坐标系中的点
         */
        CalibPoint spilcing_coor_cacl(SplicingInputTarget device1_target, SplicingInputTarget device2_target);
#pragma endregion

#pragma region 目标拼接与跟踪融合算法
        // 保存未融合目标信息的map 保存唯一id和数量
        std::unordered_map<int, int> unfused_targets_map;
        // 时间间隔
        long long dt;

        // 拼接算法输出帧率
        int splicing_fps;
        // 帧持续时间（毫秒）
        long long splicing_fps_duration;
        // 上一帧时间戳（毫秒）
        long long splicing_fps_time_ms;
        // 帧计数
        int splicing_fps_count;

        // Kalman滤波参数
        // 状态向量维度
        int kf_state_size;
        // 测量向量维度
        int kf_meas_size;
        // 控制向量维度
        int kf_control_size;
        // 预测时间间隔
        float kf_t;
        // 滤波时R矩阵比例系数
        float kf_rs_rate;
        // 滤波时Q矩阵比例系数
        float kf_qs_rate;
        // x方向测量误差
        float kf_x_error;
        // y方向测量误差
        float kf_y_error;
        // 滤波开始时间（毫秒）
        long long kf_stime;

        /**
         * 功能：对拼接目标进行跟踪预测（预测下一时刻目标位置）
         */
        void splicing_targets_track_predict();

        /**
         * 功能：根据检测结果更新跟踪目标状态
         */
        void splicing_targets_track_update();

        /**
         * 功能：融合目标权重合并
         * @param splicing_input_target_vec_ 输入目标数据缓冲区（引用）
         */
        void spiling_target_weight_merge(std::vector<SplicingInputTarget> &splicing_input_target_vec_);

        // 拼接融合ID相关参数
        // 融合ID初始值
        unsigned int splicing_fusion_id_init;
        // 融合ID最小值
        unsigned int splicing_fusion_id_min;
        // 融合ID最大值
        unsigned int splicing_fusion_id_max;
        // 融合ID计数器
        unsigned int splicing_fusion_id_count;

        /**
         * 功能：获取下一个融合ID
         * @return 下一个融合ID
         */
        unsigned int get_fusion_id();

        // 批量拼接帧计数
        unsigned int splicing_batch_frame_count;
        // 拼接目标丢失时间阈值
        long long splicing_fusion_lost_time_threshold;
        // 拼接目标轨迹保存时间阈值
        long long splicing_fusion_path_time_threshold;
        // 拼接目标初始融合时间阈值
        long long splicing_fusion_near_time_threshold;

        // 拼接输出目标集合
        std::vector<SplicingTarget> splicing_output_targets_vec;

        /**
         * 功能：删除重叠的输入目标（防止重复匹配）
         * @param temp_splicing_input_target_vec 输入目标集合（引用）
         */
        void splicing_overlap_delete(std::vector<SplicingInputTarget> &temp_splicing_input_target_vec);

        /**
         * 功能：关联目标ID，将新检测目标与已有目标进行匹配关联
         * @param temp_splicing_input_target_vec 当前输入目标集合
         * @param new_splicing_input_target_vec 输出关联后的目标集合
         */
        void splicing_output_targets_associate(std::vector<SplicingInputTarget> temp_splicing_input_target_vec, std::vector<SplicingInputTarget> &new_splicing_input_target_vec);

        /**
         * 功能：更新拼接输出目标的ID
         */
        void splicing_output_targets_id_update();

        /**
         * 功能：移除状态为REMOVED的拼接目标
         */
        void splicing_output_targets_remove();

        // 目标信息更新阈值参数
        float plate_color_update_threshold;    // 车牌颜色更新阈值
        float plate_number_update_threshold;   // 车牌号更新阈值
        float vehicle_color_update_threshold;  // 车辆颜色更新阈值
        float vehicle_type_update_threshold;   // 车辆类型更新阈值

        /**
         * 功能：更新目标信息
         * @param new_target_output 新的目标输出信息
         * @param old_target_output 旧的目标输出信息
         * @param new_target_input 新的目标检测输入
         */
        void splicing_target_info_update(TargetOutput &new_target_output, TargetOutput old_target_output, TargetTrace new_target_input);

        // 目标预测相关参数
        // 目标预测时间间隔
        long long predict_time_interval;
        // 最小移动平均周期（用于目标运动平滑预测）
        unsigned int min_moving_average_period_n;
        // 用于x轴预测的移动平均周期
        unsigned int x_moving_average_period_n;

        /**
         * 功能：对拼接目标进行模型预测（直接预测模式）
         * @param splicing_target 拼接目标对象（引用）
         * @return 预测成功返回0，失败返回-1
         */
        int splcing_target_model_predict(SplicingTarget &splicing_target);

        // 目标丢失时的匹配阈值
        float lose_x_threshold;              // 丢失时x轴预测匹配阈值
        float lose_y_threshold;              // 丢失时y轴预测匹配阈值
        float second_lose_x_threshold;       // 二次丢失时x轴预测匹配阈值
        float second_lose_y_threshold;       // 二次丢失时y轴预测匹配阈值
        float lose_speed_rate;               // 丢失时速度匹配权重系数
        float lose_plate_top1_score;         // 丢失时车牌第一候选匹配分数阈值
        float lose_plate_top2_score;         // 丢失时车牌第二候选匹配分数阈值
        double lose_match_weigth_threshold;  // 丢失时默认匹配权重阈值
        float lose_speed_threshold;          // 丢失时速度阈值
        float dead_predict_distance;         // 死区预测距离
        float max_predict_distance;          // 预测的最大距离
        float lose_number_plate_distance;    // 丢失时车牌匹配距离阈值

        /**
         * 功能：在目标丢失时，对新检测目标与丢失目标进行匹配，返回匹配结果数组
         * @param splicing_output_lose_vec 丢失目标集合
         * @param splicing_output_new_vec 新检测目标集合
         * @param match_weight 输出匹配权重矩阵
         * @return 指向匹配结果数组的指针
         */
        int* splcing_target_lose_match(std::vector<SplicingTarget> splicing_output_lose_vec, std::vector<SplicingTarget> splicing_output_new_vec, std::vector<std::vector<double>> &match_weight);

        /**
         * 功能：重新关联丢失目标与新检测目标
         */
        void splicing_output_targets_reassociate();
        void splicing_output_targets_reassociate_second();

        /**
         * 功能：新增检测目标到拼接输出目标集合中
         * @param new_splicing_input_target_vec 新检测目标集合
         */
        void splicing_output_targets_add(std::vector<SplicingInputTarget> new_splicing_input_target_vec);

        /**
         * 功能：执行拼接目标跟踪，包括预测和更新
         */
        void splicing_targets_track();

        /**
         * 功能：清理拼接输出目标集合，删除无效数据
         */
        void splicing_output_targets_vec_clean();

        /**
         * 功能：计算拼接目标的真实性分数
         */
        float cal_SplicingTarget_score(const SplicingTarget &target);
#pragma endregion

#pragma region 算法相关参数
        // 当前拼接通道ID
        int splicing_channel_id;

        // 设备映射信息（设备到基坐标系的映射）
        DevicesMap splicing_devices_map;

        // 设备标定的距离阈值
        float splicing_device_max_distance_threshold;
        float splicing_device_min_distance_threshold;

        // 判断目标是否超出设备检测区域的Y坐标阈值
        float splicing_target_out_ycoor_threshold;
        // 判断目标是否接近设备中心的Y坐标阈值
        float splicing_target_in_ycoor_threshold;

        // 是否开启噪声消除
        bool use_input_noise_filter;
        // 是否开启输出结果平滑
        bool use_output_result_smooth;
        // 是否开启碰撞检测修正
        bool use_collision_detection_correction;
#pragma endregion

#pragma region 辅助函数

        /**
         * 功能：检查输出对象是否在真实输入的对象中
         */
        bool check_target_is_real_target(OutputTarget& target);

        /**
         * 功能：计算并更新拼接输出目标的方向，将基坐标转换为车辆坐标
         */
        void splicing_output_target_get_direction();

        /**
         * 功能：将拼接输出目标集合分割为两个集合
         * 将最后一个设备的目标从splicing_output_targets中移动到splicing_output_targets_next
         */
        void split_splicing_output_targets();

        /**
         * 功能：更新拼接算法的帧率信息
         * @param timestamp_ms 当前时间戳（毫秒）
         */
        void get_splicing_fps(long long timestamp_ms);

        // 设备目标运动速度映射，key为设备ID，value为速度序列
        std::map<unsigned int, std::vector<float>> devices_speed_map;
		
        /**
         * 功能：更新设备目标速度映射
         * @param multi_device_targets 多设备检测到的目标集合
         * @param devices_speed_map_ 输出设备速度映射（引用）
         */
        void devices_speed_map_update(const MultiDeviceTargets &multi_device_targets, std::map<unsigned int, std::vector<float>> &devices_speed_map_);

        /**
         * 功能：检查两个车牌号码是否一致，并计算最终匹配得分
         * @param number_plate_1 候选车牌1
         * @param number_plate_2 候选车牌2
         * @param score_top_1 候选车牌1的匹配得分
         * @param score_top_2 候选车牌2的匹配得分
         * @param plate_score 输出最终车牌匹配得分
         * @return 0表示成功（完全一致或匹配通过），-1表示失败
         */
        int checkout_plate_ipentity(char number_plate_1[MAX_PLATE_TEXT], char number_plate_2[MAX_PLATE_TEXT], float score_top_1, float score_top_2, float &plate_score);

        /**
         * 功能：计算UTF-8字符串中每个字符的字节数
         * 注意：对于只含ASCII字符的字符串，每个字符占1字节；对于UTF-8编码的字符串，字符可能占多字节
         * @param str 输入字符串
         * @return 字符总字节数
         */
        static size_t utf8_char_size(const std::string& str);

        /**
         * 功能：打印vector中每个字符串的各字符字节数
         * @param plate_str 字符串向量
         */
        static void print_char_byte_sizes(const std::vector<std::string>& plate_str);

        /**
         * 功能：计算两点之间的角度（弧度制）
         * @param A 点A
         * @param B 点B
         * @return 两点之间的角度
         */
        static double calculateAngle(const CalibPoint & A, const CalibPoint & B);

        /**
         * 功能：对设备列表进行排序
         * @param list 指向设备列表的指针
         */
        static void deviceListSort(DeviceList *list);

        /**
         * 功能：根据中心点、角度、宽度和高度计算矩形四个角的坐标
         * @param centerPoint 矩形中心点
         * @param angle_slope 旋转角度（弧度制）
         * @param width 矩形宽度
         * @param height 矩形高度
         * @return 包含四个角点的坐标向量
         */
        static std::vector<CalibPoint> calculateRectangleCorners(CalibPoint centerPoint, double angle_slope, double width, double height);

        /**
         * 功能：根据中心点、角度和长度计算线段两端点的坐标
         * @param centerPoint 线段中心点
         * @param angle 线段角度（弧度制）
         * @param length 线段长度
         * @return 包含两个端点的坐标对
         */
        static std::pair<CalibPoint , CalibPoint> calculate_segment_endpoints(CalibPoint centerPoint, double angle, double length);

        /**
         * 功能：计算与矩形平行的线段坐标
         * @param x1 矩形第一个点x坐标
         * @param y1 矩形第一个点y坐标
         * @param x2 矩形第二个点x坐标
         * @param y2 矩形第二个点y坐标
         * @param offset 平行线段与矩形之间的偏移量
         * @return 平行线段的角点集合
         */
        static std::vector<CalibPoint> calculate_parallel_segments_with_rectangle(double x1, double y1, double x2, double y2, double offset);

        /**
         * 功能：判断一个点是否在多边形内
         * @param polygon 多边形的顶点集合
         * @param point 待判断的点
         * @return true表示在多边形内，false表示不在
         */
        static bool judgePointInRect(std::vector<CalibPoint> polygon, CalibPoint point);

        /**
         * 功能：打印MultiBoxTargets结构体的所有属性
         */
        void print_box_targets(long long timestamp_ms);

        void check_update_multi_box_targets(tecu1000_alg::MultiBoxTargets& multi_box_targets, long long timestamp_ms);
#pragma endregion

#pragma region 日志打印
        int log_info_level = 0;
#pragma endregion
    };
}