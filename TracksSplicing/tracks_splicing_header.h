#pragma once
#include <vector>
#include "tecu1000_algorithm_header.h"
#include "kalman_smooth.h"
#include "vehicleLaneSmoothheader.h"

#define SMOOTHING_MIN_SPEED_MULTIPLE 2.0
#define SMOOTHING_MAX_SPEED_MULTIPLE 3.0
#define SMOOTHING_INTERVAL_SPEED 1.0
#define SMOOTH_MIN_SPEED 80
#define SMOOTH_WINDOW_SIZE 10
#define SMOOTH_ALPHA 0.5
using namespace tecu1000_alg;
namespace tecu_r1000_0_algorithm
{
    typedef enum
    {
        New,                 //新出现的目标
        Tracked,             //目标处于跟踪状态
        Lost,				 //目标处于丢失状态
        Removed,             //目标处于删除状态
    }SplicingTrackState;

    /********************************************************
     * Description          			 目标是否在重叠区域的枚举
     * TARGET_IN_DETECTION               设备在检测区
     * TARGET_IN_TRACKING                设备在跟踪区
     * TARGET_IN_DEAD                    目标在盲区
     * TARGET_IN_VANISH                  设备在消失区
    ********************************************************/
    typedef enum
    {
        TARGET_IN_DETECTION = 0x00,
        TARGET_IN_TRACKING,
        TARGET_IN_DEAD,
        TARGET_IN_VANISH,
        ALG_TYPE_MAX
    }SplicingArea;


    /********************************************************
     * Description          			 轨迹拼接目标结构体
     * fusion_id                         目标的的融合ID
     * device_id                         输出目标的设备ID
     * id								 输出目标的ID
     * target_coor_in_base               输出目标在基坐标系的位置
     * target_coor_predict               输出目标在基坐标系的预测位置
     * target                            输入目标的原始信息
     * target_timestamp_ms               目标的时间戳
     * target_start_timestamp_ms         目标开始检测的时间戳
     * splicing_state                    目标拼接的状态
     * target_area_status                目标在重叠区域的状态
    ********************************************************/
    typedef struct
    {
        unsigned int fusion_id;
        unsigned int device_id;
        unsigned int id;
        CalibPoint   target_coor_in_base;
        CalibPoint   target_coor_predict;
        Target       target;
        long long   target_timestamp_ms;
        long long   target_start_timestamp_ms;
        SplicingTrackState splicing_state;
        SplicingArea       target_area_status;
        long long loss_time;  // 丢失时间

        float distance;    
    }TargetOutput;

    /********************************************************
     * Description          			 拼接目标信息结构体
     * target_output                     目标的输出信息
     * target_output_path                目标的输出信息轨迹
     * target_predict_xcoor              目标丢失后预测的x方向坐标
     * target_predict_ycoor              目标丢失后预测的y方向坐标
     * target_kalman					 目标丢失后的卡尔曼器， 默认设置为nullptr
     * lane_id                           目标所在的车道ID
    ********************************************************/
    typedef struct
    {
        TargetOutput				 target_output;
        float _average_distance;
        std::vector<TargetOutput>	 target_output_path;
        float                        target_predict_xcoor;
        float                        target_predict_ycoor;
        KalmanSmooth                *target_kalman = nullptr;
        int lane_id = -1;
    }SplicingTarget;



    /********************************************************
     * Description          			 设备区域判断
     * yaxis_coor_down_overlap           区域的下边界
     * yaxis_coor_up_overlap             区域的上边界
     * device1_id                        设备ID1和设备ID2的重叠区域
     * device2_id                        设备ID2和设备ID1的重叠区域
     * splicing_area                     设备区域的标识
    ********************************************************/
    typedef struct
    {
        float 						 yaxis_coor_down_overlap;
        float 						 yaxis_coor_up_overlap;
        unsigned int                 device1_id;
        unsigned int                 device2_id;
        SplicingArea                 splicing_area;
    }SplicingAreaInfo;

    /********************************************************
     * Description          			 目标的航迹信息
     * input_target						 输入数据信息
     * fusion_id                         目标的融合ID(用于多个盒子拼接，默认为0)
     * target_coor_in_base               基坐标系的坐标
     * timestamp_ms                      目标时间戳
    ********************************************************/
    typedef struct
    {
        Target target;
        unsigned int fusion_id;
        CalibPoint target_coor_in_base;
        long long time_ms;
    }TargetTrace;

    /********************************************************
     * Description          			 拼接多设备关联目标信息结构体
     * device_id                         目标被探测的设备ID
     * id                                目标的ID
     * target_area_status                目标所处的位置状态
     * target_coor_in_base               目标在基准设备坐标系下的位置
     * target_input_path                 目标的轨迹
     * last_output_target                来自上一个盒子的最后一个设备接管的设备输出 带有拼接信息
     * target_direction_update_time      目标输入的时间戳
    ********************************************************/
    typedef struct
    {
        unsigned int				 device_id;
        unsigned int			     id;
        SplicingArea                 target_area_status;
        TargetTrace                  target_input;
        std::vector<TargetTrace>	 target_input_path;
        // OutputTarget                 last_output_target;
        long long                    target_direction_update_time;
    }SplicingInputTarget;


    /********************************************************
     * Description          			 拼接匹配的信息
     * device1_id                        设备1的ID号
     * id1                               设备1探测的目标ID号
     * device2_id						 设备2的ID号
     * id2                               设备2探测的目标ID号
     * speed                             设备1和设备2探测目标的速度
     * batch_x_error                     设备1和设备2探测目标的x方向的误差
     * batch_y_error                     设备1和设备2探测目标的y方向的误差
     * splicing_update_time              设备拼接的时间戳
     * splicing_start_time               设备拼接开始的时间戳
     * splicing_batch_count              设备间目标拼接时的开始计数
     * spilcing_match_score_vec          设备的拼接分数计数
     * spilcing_batch_flag               设备拼接成功的标志
     * splicing_coor                     拼接后的目标坐标
    ********************************************************/
    typedef struct
    {
        unsigned int device1_id;
        unsigned int id1;
        unsigned int device2_id;
        unsigned int id2;
        float        speed;
        float        batch_x_error;
        float        batch_y_error;
        long long splicing_update_time;
        long long splicing_start_time;
        unsigned int splicing_batch_count;
        std::vector<float>  spilcing_match_score_vec;
        bool spilcing_batch_flag;
        CalibPoint splicing_coor;
    }SpilcingMatchIds;

    /********************************************************
     * Description          			 视觉目标与设备ID的绑定数据结构体
     * device_id                         雷视觉设备的ID号
     * id                                雷视目标的ID号
     * bvision_target                    卡口相机检测的目标信息
    ********************************************************/
    typedef struct
    {
        unsigned int  device_id;
        unsigned int  id;
        BVisionTarget bvision_target;
    }RadarVisionTarget;


    /********************************************************
     * Description grid_info        网格信息
     * @param grid_left_up          网格的上边界
     * @param grid_down_edge        网格的下边界
     *
     * @param direction_mad_std     网格内目标方向角的标准差
     * @param direction_mad_mean    网格内方向角的均值， 该均值经过平均绝对值偏差法对噪声数据进行剔除
     * @param direction_vec         网格内方位角的容器
    ********************************************************/
    typedef struct
    {
        float    grid_up_edge;
        float    grid_down_edge;

        float    direction_mad_std;
        float    direction_mad_mean;
        std::vector<float> direction_vec;
        // 为了方便序列化，我们添加一个输出到流的方法
        void printToStream(std::ostream& os) const {
            os << grid_up_edge << " "
               << grid_down_edge << " "
               << direction_mad_std << " "
               << direction_mad_mean << " ";

            os << direction_vec.size() << " "; // 首先写入向量的大小
            for (float val : direction_vec) {
                os << val << " ";
            }
            os << "\n"; // 每个 GridInfo 一行
        }
    }GridInfo;

    /********************************************************
     * Description grid_info        网格信息
     * @param road_min_coor         道路的最大目标位置
     * @param road_max_coor         道路的最小目标位置
     * @param use_ycoor             那个轴跨越的数值越大，则使用那个轴统计方位角
     * @param grid_infos            网格内方位信息
    ********************************************************/
    typedef struct
    {
        float road_min_xcoor;
        float road_max_xcoor;
        float road_min_ycoor;
        float road_max_ycoor;
        bool  use_ycoor;
        std::vector<GridInfo> grid_infos;

        // 为了方便序列化，我们添加一个输出到流的方法
        // 为了方便序列化，我们添加一个输出到流的方法
        void printToStream(std::ostream& os) const {
            os << road_min_xcoor << " "
               << road_max_xcoor << " "
               << road_min_ycoor << " "
               << road_max_ycoor << " "
               << (use_ycoor ? 1 : 0) << " ";

            os << grid_infos.size() << " "; // 首先写入 GridInfo 向量的大小
            for (const GridInfo& grid : grid_infos) {
                grid.printToStream(os);
            }
        }
    }RoadDirection;


    /********************************************************
     * Description          			 目标方位数据采集的状态
     * DIRECTION_INIT                    初始化状态
     * ROAD_RANGE_ACQUISITION            道路范围的获取阶段
     * ROAD_MESH_GENERATION              道路的网格划分阶段
     * DIRECTION_ACQUISITION             方向获取阶段
     * DIRECTION_CALC                    计算方向或相关参数的阶段
     * DIRECTION_END                     计算结束
    ********************************************************/
    typedef enum
    {
        DIRECTION_INIT = 0x00,
        ROAD_RANGE_ACQUISITION,
        ROAD_MESH_GENERATION,
        DIRECTION_ACQUISITION,
        DIRECTION_CALC,
        DIRECTION_END,
    }RoadDirectionState;
}
